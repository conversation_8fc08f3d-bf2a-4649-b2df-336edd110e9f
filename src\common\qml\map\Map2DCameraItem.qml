import QtQuick 
import QtQuick.Controls.Material
import QtQuick.Layouts
import './fovShape'

Item {
    id: _2dCameraItem
    property var _modelData: null
    property var _rootItem: null
    property var _area: null
    property int _modelIndex: -1

    property bool isPreviewHovered: false
    property var iconCoordinates: {
        "width": 0,
        "height": 0,
        "centerX": 0,
        "centerY": 0
    }

    property var mapState: {
        if (mapStateFromQML !== undefined)
            return mapStateFromQML;
        else if (map2dController)
            return map2dController.mapState;
        else
            return null;
    }

    Timer {
        id: openPreviewTimer
        interval: 1000
        repeat: false
        onTriggered: {
            previewLoader.active = true;
            previewLoader.item.playing = true;
            updatePreviewPos(iconCoordinates.width,
                        iconCoordinates.height,
                        iconCoordinates.centerX,
                        iconCoordinates.centerY);
        }
    }

    Timer {
        id: hoverTimer
        interval: 200
        repeat: false
        onTriggered: {
            // Chỉ tắt preview nếu không trong fullScreen và khi cả FOV lẫn preview đều không được hover
            if (previewLoader.item && !previewLoader.item.fullScreen &&
                !fovLoader.item.isHovered && !isPreviewHovered) {
                previewLoader.item.playing = false;
                previewLoader.active = false;
            }
        }
    }


    on_ModelDataChanged: {
        if (_modelData) {
            if (_modelData.fovMode === "ICON") {
                fovLoader.sourceComponent = arcComponent;
            } else if (_modelData.fovMode === "RECTANGLE") {
                fovLoader.sourceComponent = rectangleComponent;
            } else if (_modelData.fovMode === "POLYGON") {
                fovLoader.sourceComponent = polygonComponent;
            } else if (_modelData.fovMode === "CIRCLE") {
                fovLoader.sourceComponent = circleComponent;
            } else {
                fovLoader.sourceComponent = null;
            }
        }
    }

    Loader {
        id: fovLoader
        onLoaded: {
            if (fovLoader.item)
                fovLoader.item.camData = _2dCameraItem._modelData;
        }
    }

    Component {
        id: rectangleComponent
        RectangleShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }

            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onPosCoordChanged: function(){
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                        iconCoordinates = {
                            "width": xSize,
                            "height": ySize,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        openPreviewTimer.stop();
                        hoverTimer.start();
                    }
                }
            }
        }
    }

    Component {
        id: polygonComponent
        PolygonShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }
            
            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onBoundingRectChanged: function(){
                if (mapState && mapState.editMode && boundingRect) {
                    var itemCenter = this.mapToItem(_rootItem, boundingRect.centerX, boundingRect.centerY);
                    updateDialogPos(boundingRect.width,
                                    boundingRect.height,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onReset: function(isReset){
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.visible = !isReset;
                }
            }

            Component.onCompleted: {
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.redrawButton.visible = true;
                }
            }

            Component.onDestruction: {
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.redrawButton.visible = false;
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, boundingRect.centerX, boundingRect.centerY);
                        iconCoordinates = {
                            "width": boundingRect.width,
                            "height": boundingRect.height,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        hoverTimer.start();
                        openPreviewTimer.stop();
                    }
                }
            }
        }
    }

    Component {
        id: circleComponent
        CircleShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }

            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onPosCoordChanged: function(){
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                        iconCoordinates = {
                            "width": xSize,
                            "height": ySize,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        hoverTimer.start();
                        openPreviewTimer.stop();
                    }
                }
            }
        }
    }

    Component {
        id: arcComponent
        IconArcShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, posCoord.x + iconItem.width/2, posCoord.y);
                    updateDialogPos(iconItem.width,
                                    iconItem.height,
                                    itemCenter.x,
                                    itemCenter.y);
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }

            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onPosCoordChanged: function(){
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, posCoord.x + iconItem.width/2, posCoord.y);
                    updateDialogPos(iconItem.width,
                                    iconItem.height,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, posCoord.x + iconItem.width/2, posCoord.y + iconItem.height/2);
                        iconCoordinates = {
                            "width": iconItem.width,
                            "height": iconItem.height,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        hoverTimer.start();
                        openPreviewTimer.stop();
                    }
                }
            }
        }
    }

    Loader {
        id: previewLoader
        active: false
        sourceComponent: Rectangle {
            id: previewItem
            x: 300; y: 100; z: 200
            width: 400; height: 240
            opacity: 0.0
            parent: _rootItem
            Behavior on opacity { NumberAnimation { duration: 200 } }
            property bool playing: false
            property bool fullScreen: false
            property var initialX: x
            property var initialY: y

            Component.onCompleted: {
                initialX = x;
                initialY = y;
            }

            states: State {
                name: "visibleState"
                when: previewLoader.active
                PropertyChanges { target: previewItem; opacity: 1.0 }
            }
            transitions: Transition {
                from: ""; to: "visibleState"
                NumberAnimation { properties: "opacity,x,y"; duration: 300 }
            }

            Button {
                id: backButton
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.leftMargin: 10

                z: 201
                width: 100; height: 54
                text: qsTr("Back")
                visible: false
                background: Rectangle {
                    color: Qt.rgba(0, 0, 0, 0.8)
                    radius: 10
                }

                contentItem: Item{
                    Row {
                        spacing: 5
                        anchors.centerIn: parent
                        Image {
                            source: "qrc:/src/assets/map/left.svg"
                            width: 20
                            height: 20
                            anchors.verticalCenter: parent.verticalCenter
                        }
                        Text {
                            text: backButton.text
                            font.pixelSize: 20
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }

                onClicked: {
                    previewItem.fullScreen = false;
                }
            }

            CustomVideoOutput {
                anchors.fill: parent
                id: videoFrame
                model: _modelData
                isPlaying: playing
            }

            onFullScreenChanged: function(){
                if (playing) {
                    if (fullScreen) {
                        backButton.visible = true;
                        previewItem.x = _2dCameraItem._rootItem.x;
                        previewItem.y = _2dCameraItem._rootItem.y;
                        previewItem.width = _2dCameraItem._rootItem.width;
                        previewItem.height = _2dCameraItem._rootItem.height;
                    } else {
                        previewItem.width = 400;
                        previewItem.height = 240;

                        updatePreviewPos(previewItem.width,
                                    previewItem.height,
                                    iconCoordinates.centerX,
                                    iconCoordinates.centerY);
                        backButton.visible = false;
                        hoverTimer.start();
                    }
                }
            }

            MouseArea {
                anchors.fill: parent
                hoverEnabled: true
                onEntered: {
                    isPreviewHovered = true;  // Đánh dấu preview đang được hover
                    hoverTimer.stop();
                }
                onExited: {
                    isPreviewHovered = false;
                    if (!fovLoader.item.isHovered && !previewItem.fullScreen) {
                        hoverTimer.start();
                    }
                }
                onClicked: {
                    previewItem.fullScreen = true;
                }
            }
        }
    }

    
    Loader {
        id: configCameraFovLoader
        active: mapState ? mapState.editMode : false
        sourceComponent: ConfigCameraFovDialog {
            id: configCameraFovDialog
            visible: false
            parent: _2dCameraItem.parent
            camData: _modelData
            _mapState: mapState

            onChangeType: function(type) {
                var newFovData = getDefaultDataOfType(type);
                _2dCameraItem._modelData.fovData = JSON.stringify(newFovData);
                fovLoader.item.camData = _2dCameraItem._modelData;

                fovLoader.sourceComponent = type === "ICON" ? arcComponent :
                                            type === "RECTANGLE" ? rectangleComponent :
                                            type === "POLYGON" ? polygonComponent :
                                            type === "CIRCLE" ? circleComponent : arcComponent;

                if(type === "POLYGON"){
                    fovLoader.item.reset(true);
                }
            }

            onCamDataUpdated: function(newData) {
                if (fovLoader.item) {
                    fovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                    
                    if (fovLoader.item.requestPaint) {
                        fovLoader.item.requestPaint();
                    }
                }
            }

            onDeleteCamera: function() {
                if (map2dController && _modelIndex >= 0) {
                    map2dController.temporarilyDeleteCamera(_modelIndex);
                }
            }

            onRedrawPolygon: function() {
                if (fovLoader.item) {
                    fovLoader.item.resetPoints();
                }
            }
        }
    }

    function updatePreviewPos(iconWidth, iconHeight, iconCenterX, iconCenterY) {
        var offset = 10;
        var dialog = previewLoader.item;
        var dialogW = dialog.width;
        var dialogH = dialog.height;

        // biên của vùng cho phép
        var areaL = _area.x;
        var areaT = _area.y;
        var areaR = _area.x + _area.width;
        var areaB = _area.y + _area.height;

        // toạ độ cạnh của icon
        var iconL = iconCenterX - iconWidth/2;
        var iconR = iconCenterX + iconWidth/2;
        var iconT = iconCenterY - iconHeight/2;
        var iconB = iconCenterY + iconHeight/2;

        // vị trí mặc định: nằm giữa ngang, bên dưới icon
        var x = iconCenterX - dialogW/2;
        var y = iconB + offset;

        // nếu bên dưới không vừa, thử lên trên
        if (y + dialogH > areaB) {
            var yAbove = iconT - offset - dialogH;
            if (yAbove >= areaT) {
                y = yAbove;
            } else {
                // nếu cũng không vừa lên trên, thử bên phải
                var xRight = iconR + offset;
                if (xRight + dialogW <= areaR) {
                    x = xRight;
                    y = iconCenterY - dialogH/2;
                } else {
                    // thử bên trái
                    var xLeft = iconL - offset - dialogW;
                    if (xLeft >= areaL) {
                        x = xLeft;
                        y = iconCenterY - dialogH/2;
                    } else {
                        // fallback: clamp vào vùng
                        x = Math.min(Math.max(x, areaL), areaR - dialogW);
                        y = Math.min(Math.max(y, areaT), areaB - dialogH);
                    }
                }
            }
        }

        // luôn clamp để không vượt biên
        x = Math.min(Math.max(x, areaL), areaR - dialogW);
        y = Math.min(Math.max(y, areaT), areaB - dialogH);

        // chuyển về hệ toạ độ của parent và gán cho dialog
        // var finalPos = _rootItem.mapToItem(previewItem.parent, x, y);
        dialog.x = x;
        dialog.y = y;
    }

    
    function getDefaultDataOfType(type) {
        var position = (_2dCameraItem._modelData.fovData ? JSON.parse(_2dCameraItem._modelData.fovData) : {}).position;
        var ratio = (_area ? (_area.scaleRatio ? _area.scaleRatio : 1) : 1);
        switch (type) {
            case "ICON":
                return {
                    'position': position,
                    'arc_start_angle': -45,
                    'arc_range_angle': 90,
                    'radius': 80 / ratio,
                    'icon_type': 0
                }
            case "RECTANGLE":
                return {
                    'position': position,
                    'width': 100 / ratio,
                    'height': 100 / ratio
                }
            case "POLYGON":
                return {
                    'position': position,
                    'points': []
                }
            case "CIRCLE":
                return {
                    'position': position,
                    'width': 100 / ratio,
                    'height': 100 / ratio
                }
            default:
                return {
                    'position': position,
                    'arc_start_angle': -45,
                    'arc_range_angle': 90,
                    'radius': 80 / ratio,
                    'icon_type': 0
                }
        }
    }

    function updateDialogPos(iconWidth, iconHeight, iconCenterX, iconCenterY) {
        var offset = 10;
        var dialog = configCameraFovLoader.item;
        var dialogW = dialog.width;
        var dialogH = dialog.height;

        // biên của vùng cho phép
        var areaL = _area.x;
        var areaT = _area.y;
        var areaR = _area.x + _area.width;
        var areaB = _area.y + _area.height;

        // toạ độ cạnh của icon
        var iconL = iconCenterX - iconWidth/2;
        var iconR = iconCenterX + iconWidth/2;
        var iconT = iconCenterY - iconHeight/2;
        var iconB = iconCenterY + iconHeight/2;

        // vị trí mặc định: nằm giữa ngang, bên dưới icon
        var x = iconCenterX - dialogW/2;
        var y = iconB + offset;

        // nếu bên dưới không vừa, thử lên trên
        if (y + dialogH > areaB) {
            var yAbove = iconT - offset - dialogH;
            if (yAbove >= areaT) {
                y = yAbove;
            } else {
                // nếu cũng không vừa lên trên, thử bên phải
                var xRight = iconR + offset;
                if (xRight + dialogW <= areaR) {
                    x = xRight;
                    y = iconCenterY - dialogH/2;
                } else {
                    // thử bên trái
                    var xLeft = iconL - offset - dialogW;
                    if (xLeft >= areaL) {
                        x = xLeft;
                        y = iconCenterY - dialogH/2;
                    } else {
                        // fallback: clamp vào vùng
                        x = Math.min(Math.max(x, areaL), areaR - dialogW);
                        y = Math.min(Math.max(y, areaT), areaB - dialogH);
                    }
                }
            }
        }

        // luôn clamp để không vượt biên
        x = Math.min(Math.max(x, areaL), areaR - dialogW);
        y = Math.min(Math.max(y, areaT), areaB - dialogH);

        // chuyển về hệ toạ độ của parent và gán cho dialog
        var finalPos = _rootItem.mapToItem(_2dCameraItem.parent, x, y);
        dialog.x = finalPos.x;
        dialog.y = finalPos.y;
    }
}
